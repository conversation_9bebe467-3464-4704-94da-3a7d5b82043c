/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/about`; params?: Router.UnknownInputParams; } | { pathname: `/../assets/styles/auth.styles`; params?: Router.UnknownInputParams; } | { pathname: `/../constants/colors`; params?: Router.UnknownInputParams; } | { pathname: `/../assets/styles/create.styles`; params?: Router.UnknownInputParams; } | { pathname: `/../assets/styles/home.styles`; params?: Router.UnknownInputParams; } | { pathname: `/../libs/utils`; params?: Router.UnknownInputParams; } | { pathname: `/../components/SafeScreen`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/about`; params?: Router.UnknownOutputParams; } | { pathname: `/../assets/styles/auth.styles`; params?: Router.UnknownOutputParams; } | { pathname: `/../constants/colors`; params?: Router.UnknownOutputParams; } | { pathname: `/../assets/styles/create.styles`; params?: Router.UnknownOutputParams; } | { pathname: `/../assets/styles/home.styles`; params?: Router.UnknownOutputParams; } | { pathname: `/../libs/utils`; params?: Router.UnknownOutputParams; } | { pathname: `/../components/SafeScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/about${`?${string}` | `#${string}` | ''}` | `/../assets/styles/auth.styles${`?${string}` | `#${string}` | ''}` | `/../constants/colors${`?${string}` | `#${string}` | ''}` | `/../assets/styles/create.styles${`?${string}` | `#${string}` | ''}` | `/../assets/styles/home.styles${`?${string}` | `#${string}` | ''}` | `/../libs/utils${`?${string}` | `#${string}` | ''}` | `/../components/SafeScreen${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/about`; params?: Router.UnknownInputParams; } | { pathname: `/../assets/styles/auth.styles`; params?: Router.UnknownInputParams; } | { pathname: `/../constants/colors`; params?: Router.UnknownInputParams; } | { pathname: `/../assets/styles/create.styles`; params?: Router.UnknownInputParams; } | { pathname: `/../assets/styles/home.styles`; params?: Router.UnknownInputParams; } | { pathname: `/../libs/utils`; params?: Router.UnknownInputParams; } | { pathname: `/../components/SafeScreen`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; };
    }
  }
}
